import { Link } from 'react-router-dom';
import { Heart, Facebook, Twitter, Instagram, Linkedin, Mail, Phone, MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { contactInfo } from '@/data/mockData';

const footerLinks = {
  'About': [
    { name: 'Our Mission', href: '/about#mission' },
    { name: 'Leadership Team', href: '/about#team' },
    { name: 'Annual Reports', href: '/transparency' },
    { name: 'Careers', href: '/careers' }
  ],
  'Programs': [
    { name: 'Education', href: '/programs?category=education' },
    { name: 'Healthcare', href: '/programs?category=healthcare' },
    { name: 'Environment', href: '/programs?category=environment' },
    { name: 'Community Development', href: '/programs?category=community-development' }
  ],
  'Get Involved': [
    { name: 'Donate', href: '/donate' },
    { name: 'Volunteer', href: '/volunteer' },
    { name: 'Corporate Partnerships', href: '/partnerships' },
    { name: 'Fundraise for Us', href: '/fundraise' }
  ],
  'Resources': [
    { name: 'News & Updates', href: '/news' },
    { name: 'Impact Stories', href: '/news?category=success-stories' },
    { name: 'Financial Transparency', href: '/transparency' },
    { name: 'Contact Us', href: '/contact' }
  ]
};

const socialLinks = [
  { name: 'Facebook', icon: Facebook, href: contactInfo.socialMedia.facebook },
  { name: 'Twitter', icon: Twitter, href: contactInfo.socialMedia.twitter },
  { name: 'Instagram', icon: Instagram, href: contactInfo.socialMedia.instagram },
  { name: 'LinkedIn', icon: Linkedin, href: contactInfo.socialMedia.linkedin }
];

export function Footer() {
  return (
    <footer className="bg-gray-900 text-white">
      {/* Newsletter Signup */}
      <div className="bg-blue-600 py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h3 className="text-2xl font-bold mb-4">Stay Connected</h3>
            <p className="text-blue-100 mb-6">
              Get updates on our programs, success stories, and ways to make a difference
            </p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <Input
                type="email"
                placeholder="Enter your email"
                className="bg-white text-gray-900"
              />
              <Button variant="secondary" className="bg-white text-blue-600 hover:bg-gray-100">
                Subscribe
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Footer Content */}
      <div className="py-12">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
            {/* Organization Info */}
            <div className="lg:col-span-2">
              <Link to="/" className="flex items-center space-x-3 mb-4">
                <div className="bg-blue-600 p-2 rounded-lg">
                  <Heart className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h2 className="text-lg font-bold">Hope Foundation</h2>
                  <p className="text-sm text-gray-400">Empowering Communities</p>
                </div>
              </Link>
              <p className="text-gray-300 mb-4 text-sm leading-relaxed">
                Since 2010, we've been dedicated to creating sustainable positive change 
                in underserved communities through education, healthcare, and economic 
                empowerment programs.
              </p>
              
              {/* Contact Info */}
              <div className="space-y-2 text-sm">
                <div className="flex items-center space-x-2">
                  <MapPin className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-300">
                    {contactInfo.address.street}, {contactInfo.address.city}, {contactInfo.address.state}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <Phone className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-300">{contactInfo.phone}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Mail className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-300">{contactInfo.email}</span>
                </div>
              </div>
            </div>

            {/* Footer Links */}
            {Object.entries(footerLinks).map(([category, links]) => (
              <div key={category}>
                <h3 className="font-semibold mb-4">{category}</h3>
                <ul className="space-y-2">
                  {links.map((link) => (
                    <li key={link.name}>
                      <Link
                        to={link.href}
                        className="text-gray-300 hover:text-white text-sm transition-colors"
                      >
                        {link.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-gray-800 py-6">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            {/* Copyright */}
            <div className="text-sm text-gray-400">
              <p>© 2024 Hope Foundation Trust. All rights reserved.</p>
              <p>Tax-Exempt Organization | Registration: TR-2010-001234</p>
            </div>

            {/* Social Links */}
            <div className="flex items-center space-x-4">
              {socialLinks.map((social) => {
                const Icon = social.icon;
                return (
                  <a
                    key={social.name}
                    href={social.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-gray-400 hover:text-white transition-colors"
                    aria-label={social.name}
                  >
                    <Icon className="h-5 w-5" />
                  </a>
                );
              })}
            </div>

            {/* Legal Links */}
            <div className="flex items-center space-x-4 text-sm">
              <Link to="/privacy" className="text-gray-400 hover:text-white">
                Privacy Policy
              </Link>
              <Link to="/terms" className="text-gray-400 hover:text-white">
                Terms of Service
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
