import type { 
  TrustInfo, 
  ImpactStats, 
  TeamMember, 
  Program, 
  DonationOption, 
  NewsArticle, 
  ContactInfo,
  FinancialReport 
} from '@/types';

export const trustInfo: TrustInfo = {
  name: "Hope Foundation Trust",
  tagline: "Empowering Communities, Transforming Lives",
  mission: "To create sustainable positive change in underserved communities through education, healthcare, and economic empowerment programs that address root causes of poverty and inequality.",
  vision: "A world where every individual has access to opportunities for growth, dignity, and a better future, regardless of their background or circumstances.",
  founded: 2010,
  registrationNumber: "TR-***********",
  taxExemptStatus: "501(c)(3) Tax-Exempt Organization"
};

export const impactStats: ImpactStats = {
  beneficiariesServed: 25000,
  programsActive: 12,
  volunteersActive: 450,
  fundsRaised: 2500000,
  yearsOfService: 14
};

export const teamMembers: TeamMember[] = [
  {
    id: "1",
    name: "Dr. <PERSON>",
    position: "Executive Director",
    bio: "Dr. <PERSON> brings over 15 years of experience in nonprofit leadership and community development. She holds a PhD in Social Work and has led transformative programs across three continents.",
    image: "/images/team/sarah-johnson.jpg",
    email: "<EMAIL>",
    linkedin: "https://linkedin.com/in/sarah<PERSON><PERSON>son",
    expertise: ["Nonprofit Leadership", "Community Development", "Strategic Planning"]
  },
  {
    id: "2",
    name: "Michael Chen",
    position: "Program Director",
    bio: "Michael oversees all program implementation and evaluation. With a background in international development and project management, he ensures our programs deliver measurable impact.",
    image: "/images/team/michael-chen.jpg",
    email: "<EMAIL>",
    expertise: ["Program Management", "Impact Measurement", "International Development"]
  },
  {
    id: "3",
    name: "Dr. Priya Patel",
    position: "Healthcare Initiative Lead",
    bio: "Dr. Patel is a practicing physician who leads our healthcare programs. She has established mobile clinics and health education programs in rural communities.",
    image: "/images/team/priya-patel.jpg",
    email: "<EMAIL>",
    expertise: ["Public Health", "Rural Healthcare", "Medical Outreach"]
  },
  {
    id: "4",
    name: "James Rodriguez",
    position: "Education Program Manager",
    bio: "James manages our education initiatives, including scholarship programs and school infrastructure projects. He has a Master's in Education Policy.",
    image: "/images/team/james-rodriguez.jpg",
    email: "<EMAIL>",
    expertise: ["Education Policy", "Curriculum Development", "Youth Programs"]
  }
];

export const programs: Program[] = [
  {
    id: "1",
    title: "Rural Education Initiative",
    description: "Building schools and providing quality education in remote rural areas",
    longDescription: "Our Rural Education Initiative focuses on establishing educational infrastructure in underserved rural communities. We build schools, train teachers, provide learning materials, and implement technology solutions to bridge the education gap.",
    image: "/images/programs/rural-education.jpg",
    category: "education",
    status: "active",
    beneficiaries: 5000,
    budget: 500000,
    startDate: "2020-01-15",
    location: "Rural Districts, State",
    gallery: ["/images/programs/rural-education-1.jpg", "/images/programs/rural-education-2.jpg"]
  },
  {
    id: "2",
    title: "Mobile Healthcare Clinics",
    description: "Bringing essential healthcare services to remote communities",
    longDescription: "Our mobile healthcare program operates a fleet of medical vehicles that travel to remote areas, providing basic healthcare, preventive care, health education, and emergency medical services to communities with limited access to healthcare facilities.",
    image: "/images/programs/mobile-healthcare.jpg",
    category: "healthcare",
    status: "active",
    beneficiaries: 8000,
    budget: 750000,
    startDate: "2019-06-01",
    location: "Multiple Rural Areas",
    gallery: ["/images/programs/mobile-healthcare-1.jpg", "/images/programs/mobile-healthcare-2.jpg"]
  },
  {
    id: "3",
    title: "Clean Water Project",
    description: "Installing water purification systems and wells in water-scarce regions",
    longDescription: "This initiative addresses water scarcity by installing solar-powered water purification systems, drilling wells, and educating communities about water conservation and hygiene practices.",
    image: "/images/programs/clean-water.jpg",
    category: "environment",
    status: "active",
    beneficiaries: 12000,
    budget: 300000,
    startDate: "2021-03-10",
    location: "Drought-Affected Areas",
    gallery: ["/images/programs/clean-water-1.jpg", "/images/programs/clean-water-2.jpg"]
  }
];

export const donationOptions: DonationOption[] = [
  {
    id: "1",
    title: "General Fund",
    description: "Support our overall mission and allow us to direct funds where they're needed most",
    suggestedAmounts: [25, 50, 100, 250, 500],
    category: "general",
    impact: "Your donation helps us maintain and expand all our programs"
  },
  {
    id: "2",
    title: "Education Support",
    description: "Fund scholarships, school supplies, and educational infrastructure",
    suggestedAmounts: [30, 75, 150, 300],
    category: "education",
    impact: "$150 can provide school supplies for 10 children for a full year"
  },
  {
    id: "3",
    title: "Healthcare Fund",
    description: "Support mobile clinics, medical supplies, and health education programs",
    suggestedAmounts: [40, 100, 200, 500],
    category: "healthcare",
    impact: "$200 can provide basic healthcare for a family of four for six months"
  },
  {
    id: "4",
    title: "Emergency Relief",
    description: "Rapid response fund for natural disasters and humanitarian crises",
    suggestedAmounts: [50, 100, 250, 500, 1000],
    category: "emergency",
    impact: "$100 can provide emergency supplies for a displaced family"
  }
];

export const newsArticles: NewsArticle[] = [
  {
    id: "1",
    title: "New School Opens in Remote Mountain Village",
    excerpt: "After 18 months of construction, our newest school facility opened its doors to 200 children in the remote village of Hillcrest.",
    content: "Full article content would go here...",
    author: "Sarah Johnson",
    publishDate: "2024-06-15",
    category: "success-stories",
    tags: ["education", "infrastructure", "rural development"],
    featuredImage: "/images/news/school-opening.jpg",
    isPublished: true,
    views: 1250
  },
  {
    id: "2",
    title: "Annual Impact Report 2023 Released",
    excerpt: "Our comprehensive 2023 impact report shows significant growth in all program areas, with 25,000 beneficiaries served.",
    content: "Full article content would go here...",
    author: "Michael Chen",
    publishDate: "2024-05-20",
    category: "impact-reports",
    tags: ["annual report", "impact", "transparency"],
    featuredImage: "/images/news/impact-report.jpg",
    isPublished: true,
    views: 890
  }
];

export const contactInfo: ContactInfo = {
  address: {
    street: "123 Charity Lane",
    city: "Compassion City",
    state: "CA",
    zipCode: "90210",
    country: "United States"
  },
  phone: "+****************",
  email: "<EMAIL>",
  website: "https://hopefoundation.org",
  socialMedia: {
    facebook: "https://facebook.com/hopefoundationtrust",
    twitter: "https://twitter.com/hopefoundation",
    instagram: "https://instagram.com/hopefoundationtrust",
    linkedin: "https://linkedin.com/company/hope-foundation-trust"
  },
  officeHours: {
    weekdays: "Monday - Friday: 9:00 AM - 6:00 PM",
    weekends: "Saturday: 10:00 AM - 2:00 PM (Closed Sundays)"
  }
};

export const financialReports: FinancialReport[] = [
  {
    id: "1",
    year: 2023,
    title: "Annual Financial Report 2023",
    type: "annual",
    fileUrl: "/reports/annual-report-2023.pdf",
    summary: {
      totalRevenue: 2500000,
      totalExpenses: 2200000,
      programExpenses: 1870000,
      administrativeExpenses: 220000,
      fundraisingExpenses: 110000,
      netAssets: 300000
    },
    publishDate: "2024-03-15"
  },
  {
    id: "2",
    year: 2022,
    title: "Annual Financial Report 2022",
    type: "annual",
    fileUrl: "/reports/annual-report-2022.pdf",
    summary: {
      totalRevenue: 2100000,
      totalExpenses: 1950000,
      programExpenses: 1560000,
      administrativeExpenses: 195000,
      fundraisingExpenses: 195000,
      netAssets: 150000
    },
    publishDate: "2023-03-20"
  }
];
