import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Layout } from '@/components/layout/Layout';
import { HomePage } from '@/pages/HomePage';
import { AboutPage } from '@/pages/AboutPage';
import { ProgramsPage } from '@/pages/ProgramsPage';
import { DonatePage } from '@/pages/DonatePage';
import { VolunteerPage } from '@/pages/VolunteerPage';
import { NewsPage } from '@/pages/NewsPage';
import { TransparencyPage } from '@/pages/TransparencyPage';
import { ContactPage } from '@/pages/ContactPage';
import "./index.css";

export function App() {
  return (
    <Router>
      <Layout>
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/about" element={<AboutPage />} />
          <Route path="/programs" element={<ProgramsPage />} />
          <Route path="/donate" element={<DonatePage />} />
          <Route path="/volunteer" element={<VolunteerPage />} />
          <Route path="/get-involved" element={<VolunteerPage />} />
          <Route path="/news" element={<NewsPage />} />
          <Route path="/transparency" element={<TransparencyPage />} />
          <Route path="/contact" element={<ContactPage />} />
        </Routes>
      </Layout>
    </Router>
  );
}

export default App;
