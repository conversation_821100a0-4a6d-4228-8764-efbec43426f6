// Core types for the charitable trust website

export interface TrustInfo {
  name: string;
  tagline: string;
  mission: string;
  vision: string;
  founded: number;
  registrationNumber: string;
  taxExemptStatus: string;
}

export interface ImpactStats {
  beneficiariesServed: number;
  programsActive: number;
  volunteersActive: number;
  fundsRaised: number;
  yearsOfService: number;
}

export interface TeamMember {
  id: string;
  name: string;
  position: string;
  bio: string;
  image?: string;
  email?: string;
  linkedin?: string;
  expertise: string[];
}

export interface Program {
  id: string;
  title: string;
  description: string;
  longDescription: string;
  image: string;
  category: ProgramCategory;
  status: 'active' | 'completed' | 'planned';
  beneficiaries: number;
  budget: number;
  startDate: string;
  endDate?: string;
  location: string;
  gallery?: string[];
}

export type ProgramCategory = 
  | 'education'
  | 'healthcare'
  | 'environment'
  | 'poverty-alleviation'
  | 'disaster-relief'
  | 'community-development';

export interface DonationOption {
  id: string;
  title: string;
  description: string;
  suggestedAmounts: number[];
  category: DonationCategory;
  isRecurring?: boolean;
  impact: string;
}

export type DonationCategory = 
  | 'general'
  | 'education'
  | 'healthcare'
  | 'emergency'
  | 'infrastructure';

export interface Donation {
  id: string;
  amount: number;
  currency: string;
  donorName: string;
  donorEmail: string;
  category: DonationCategory;
  isRecurring: boolean;
  frequency?: 'monthly' | 'quarterly' | 'annually';
  message?: string;
  isAnonymous: boolean;
  date: string;
  status: 'pending' | 'completed' | 'failed';
  paymentMethod: string;
  transactionId?: string;
}

export interface Volunteer {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  skills: string[];
  interests: ProgramCategory[];
  availability: {
    days: string[];
    timeSlots: string[];
    hoursPerWeek: number;
  };
  experience: string;
  motivation: string;
  status: 'pending' | 'approved' | 'active' | 'inactive';
  joinDate?: string;
  hoursContributed?: number;
}

export interface NewsArticle {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  author: string;
  publishDate: string;
  category: NewsCategory;
  tags: string[];
  featuredImage: string;
  gallery?: string[];
  isPublished: boolean;
  views: number;
}

export type NewsCategory = 
  | 'success-stories'
  | 'announcements'
  | 'events'
  | 'impact-reports'
  | 'partnerships';

export interface ContactInfo {
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  phone: string;
  email: string;
  website: string;
  socialMedia: {
    facebook?: string;
    twitter?: string;
    instagram?: string;
    linkedin?: string;
    youtube?: string;
  };
  officeHours: {
    weekdays: string;
    weekends: string;
  };
}

export interface FinancialReport {
  id: string;
  year: number;
  title: string;
  type: 'annual' | 'quarterly' | 'impact';
  fileUrl: string;
  summary: {
    totalRevenue: number;
    totalExpenses: number;
    programExpenses: number;
    administrativeExpenses: number;
    fundraisingExpenses: number;
    netAssets: number;
  };
  publishDate: string;
}

export interface ContactMessage {
  id: string;
  name: string;
  email: string;
  phone?: string;
  subject: string;
  message: string;
  category: 'general' | 'volunteer' | 'donation' | 'partnership' | 'media';
  date: string;
  status: 'new' | 'in-progress' | 'resolved';
  assignedTo?: string;
}

// Form types
export interface DonationFormData {
  amount: number;
  customAmount?: number;
  category: DonationCategory;
  isRecurring: boolean;
  frequency?: 'monthly' | 'quarterly' | 'annually';
  donorInfo: {
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
    address?: {
      street: string;
      city: string;
      state: string;
      zipCode: string;
    };
  };
  paymentMethod: 'card' | 'bank' | 'paypal';
  message?: string;
  isAnonymous: boolean;
  newsletter: boolean;
}

export interface VolunteerFormData {
  personalInfo: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    dateOfBirth: string;
    address: {
      street: string;
      city: string;
      state: string;
      zipCode: string;
    };
  };
  skills: string[];
  interests: ProgramCategory[];
  availability: {
    days: string[];
    timeSlots: string[];
    hoursPerWeek: number;
  };
  experience: string;
  motivation: string;
  references?: {
    name: string;
    relationship: string;
    phone: string;
    email: string;
  }[];
  backgroundCheck: boolean;
  emergencyContact: {
    name: string;
    relationship: string;
    phone: string;
  };
}

export interface ContactFormData {
  name: string;
  email: string;
  phone?: string;
  subject: string;
  category: 'general' | 'volunteer' | 'donation' | 'partnership' | 'media';
  message: string;
}
