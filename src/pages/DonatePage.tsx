import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { 
  Heart, 
  CreditCard, 
  Shield, 
  Users,
  GraduationCap,
  Stethoscope,
  AlertTriangle
} from 'lucide-react';
import { donationOptions } from '@/data/mockData';

export function DonatePage() {
  const [selectedAmount, setSelectedAmount] = useState<number | null>(null);
  const [customAmount, setCustomAmount] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('general');
  const [isRecurring, setIsRecurring] = useState(false);

  const selectedOption = donationOptions.find(option => option.category === selectedCategory);

  return (
    <div className="space-y-0">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-red-600 to-pink-800 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl lg:text-5xl font-bold mb-6">Make a Donation</h1>
            <p className="text-xl text-red-100 leading-relaxed">
              Your generosity helps us create lasting positive change in communities worldwide
            </p>
          </div>
        </div>
      </section>

      {/* Donation Form */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="grid lg:grid-cols-3 gap-8">
              {/* Donation Options */}
              <div className="lg:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Heart className="h-6 w-6 text-red-600" />
                      <span>Choose Your Donation</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Category Selection */}
                    <div>
                      <Label className="text-base font-semibold mb-3 block">Select a cause</Label>
                      <div className="grid md:grid-cols-2 gap-3">
                        {donationOptions.map((option) => (
                          <Card 
                            key={option.id}
                            className={`cursor-pointer transition-all ${
                              selectedCategory === option.category 
                                ? 'ring-2 ring-blue-600 bg-blue-50' 
                                : 'hover:shadow-md'
                            }`}
                            onClick={() => setSelectedCategory(option.category)}
                          >
                            <CardContent className="p-4">
                              <div className="flex items-start space-x-3">
                                <div className="bg-blue-100 p-2 rounded-lg">
                                  {option.category === 'education' && <GraduationCap className="h-5 w-5 text-blue-600" />}
                                  {option.category === 'healthcare' && <Stethoscope className="h-5 w-5 text-green-600" />}
                                  {option.category === 'emergency' && <AlertTriangle className="h-5 w-5 text-red-600" />}
                                  {option.category === 'general' && <Heart className="h-5 w-5 text-purple-600" />}
                                </div>
                                <div className="flex-1">
                                  <h3 className="font-semibold text-gray-900">{option.title}</h3>
                                  <p className="text-sm text-gray-600 mt-1">{option.description}</p>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </div>

                    {/* Amount Selection */}
                    <div>
                      <Label className="text-base font-semibold mb-3 block">Choose amount</Label>
                      <div className="grid grid-cols-3 md:grid-cols-5 gap-3 mb-4">
                        {selectedOption?.suggestedAmounts.map((amount) => (
                          <Button
                            key={amount}
                            variant={selectedAmount === amount ? "default" : "outline"}
                            onClick={() => {
                              setSelectedAmount(amount);
                              setCustomAmount('');
                            }}
                            className="h-12"
                          >
                            ₹{amount}
                          </Button>
                        ))}
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Label htmlFor="custom-amount">Custom amount:</Label>
                        <div className="relative flex-1 max-w-xs">
                          <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">₹</span>
                          <Input
                            id="custom-amount"
                            type="number"
                            placeholder="0"
                            value={customAmount}
                            onChange={(e) => {
                              setCustomAmount(e.target.value);
                              setSelectedAmount(null);
                            }}
                            className="pl-8"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Recurring Donation */}
                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        id="recurring"
                        checked={isRecurring}
                        onChange={(e) => setIsRecurring(e.target.checked)}
                        className="rounded border-gray-300"
                      />
                      <Label htmlFor="recurring" className="text-sm">
                        Make this a monthly recurring donation
                      </Label>
                    </div>

                    {/* Donor Information */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">Donor Information</h3>
                      <div className="grid md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="firstName">First Name</Label>
                          <Input id="firstName" placeholder="John" />
                        </div>
                        <div>
                          <Label htmlFor="lastName">Last Name</Label>
                          <Input id="lastName" placeholder="Doe" />
                        </div>
                        <div>
                          <Label htmlFor="email">Email</Label>
                          <Input id="email" type="email" placeholder="<EMAIL>" />
                        </div>
                        <div>
                          <Label htmlFor="phone">Phone (Optional)</Label>
                          <Input id="phone" type="tel" placeholder="+****************" />
                        </div>
                      </div>
                    </div>

                    {/* Payment Method */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">Payment Method</h3>
                      <Select defaultValue="card">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="card">Credit/Debit Card</SelectItem>
                          <SelectItem value="paypal">PayPal</SelectItem>
                          <SelectItem value="bank">Bank Transfer</SelectItem>
                        </SelectContent>
                      </Select>
                      
                      <div className="grid md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="cardNumber">Card Number</Label>
                          <Input id="cardNumber" placeholder="1234 5678 9012 3456" />
                        </div>
                        <div>
                          <Label htmlFor="expiryDate">Expiry Date</Label>
                          <Input id="expiryDate" placeholder="MM/YY" />
                        </div>
                        <div>
                          <Label htmlFor="cvv">CVV</Label>
                          <Input id="cvv" placeholder="123" />
                        </div>
                        <div>
                          <Label htmlFor="zipCode">ZIP Code</Label>
                          <Input id="zipCode" placeholder="12345" />
                        </div>
                      </div>
                    </div>

                    <Button size="lg" className="w-full">
                      <CreditCard className="mr-2 h-5 w-5" />
                      Donate ₹{selectedAmount || customAmount || '0'}
                      {isRecurring ? ' Monthly' : ''}
                    </Button>
                  </CardContent>
                </Card>
              </div>

              {/* Donation Impact & Security */}
              <div className="space-y-6">
                {/* Impact Information */}
                {selectedOption && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Your Impact</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-gray-600 text-sm leading-relaxed">
                        {selectedOption.impact}
                      </p>
                    </CardContent>
                  </Card>
                )}

                {/* Security Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center space-x-2">
                      <Shield className="h-5 w-5 text-green-600" />
                      <span>Secure Donation</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Badge variant="secondary">SSL Encrypted</Badge>
                    </div>
                    <p className="text-sm text-gray-600">
                      Your donation is processed securely. We never store your payment information.
                    </p>
                    <p className="text-sm text-gray-600">
                      Tax-deductible receipt will be emailed to you.
                    </p>
                  </CardContent>
                </Card>

                {/* Recent Donors */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center space-x-2">
                      <Users className="h-5 w-5 text-blue-600" />
                      <span>Recent Supporters</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 text-sm">
                      <p><strong>Anonymous</strong> donated ₹2,500</p>
                      <p><strong>Priya S.</strong> donated ₹1,000</p>
                      <p><strong>Rajesh K.</strong> donated ₹500</p>
                      <p className="text-gray-500">Join 1,234+ donors this month</p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
