import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Heart, 
  Users, 
  Target, 
  TrendingUp, 
  ArrowRight,
  GraduationCap,
  Stethoscope,
  Droplets,
  HandHeart
} from 'lucide-react';
import { trustInfo, impactStats, programs } from '@/data/mockData';

export function HomePage() {
  return (
    <div className="space-y-0">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 text-white py-20 lg:py-32">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl lg:text-6xl font-bold mb-6 leading-tight">
              {trustInfo.tagline}
            </h1>
            <p className="text-xl lg:text-2xl mb-8 text-blue-100 leading-relaxed">
              {trustInfo.mission}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" asChild className="bg-white text-blue-600 hover:bg-gray-100">
                <Link to="/donate">
                  Donate Now <Heart className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" asChild className="border-white text-white hover:bg-white hover:text-blue-600">
                <Link to="/volunteer">
                  Become a Volunteer <Users className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Impact Statistics */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Our Impact in Numbers
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Since {trustInfo.founded}, we've been making a measurable difference in communities worldwide
            </p>
          </div>
          
          <div className="grid grid-cols-2 lg:grid-cols-5 gap-6">
            <Card className="text-center p-6 hover:shadow-lg transition-shadow">
              <CardContent className="p-0">
                <div className="text-3xl lg:text-4xl font-bold text-blue-600 mb-2">
                  {impactStats.beneficiariesServed.toLocaleString()}+
                </div>
                <div className="text-gray-600 font-medium">Lives Impacted</div>
              </CardContent>
            </Card>
            
            <Card className="text-center p-6 hover:shadow-lg transition-shadow">
              <CardContent className="p-0">
                <div className="text-3xl lg:text-4xl font-bold text-green-600 mb-2">
                  {impactStats.programsActive}
                </div>
                <div className="text-gray-600 font-medium">Active Programs</div>
              </CardContent>
            </Card>
            
            <Card className="text-center p-6 hover:shadow-lg transition-shadow">
              <CardContent className="p-0">
                <div className="text-3xl lg:text-4xl font-bold text-purple-600 mb-2">
                  {impactStats.volunteersActive}+
                </div>
                <div className="text-gray-600 font-medium">Active Volunteers</div>
              </CardContent>
            </Card>
            
            <Card className="text-center p-6 hover:shadow-lg transition-shadow">
              <CardContent className="p-0">
                <div className="text-3xl lg:text-4xl font-bold text-orange-600 mb-2">
                  ₹{(impactStats.fundsRaised / 1000000).toFixed(1)}M
                </div>
                <div className="text-gray-600 font-medium">Funds Raised</div>
              </CardContent>
            </Card>
            
            <Card className="text-center p-6 hover:shadow-lg transition-shadow">
              <CardContent className="p-0">
                <div className="text-3xl lg:text-4xl font-bold text-red-600 mb-2">
                  {impactStats.yearsOfService}+
                </div>
                <div className="text-gray-600 font-medium">Years of Service</div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                Our Mission & Vision
              </h2>
              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="bg-blue-100 p-3 rounded-lg">
                    <Target className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Mission</h3>
                    <p className="text-gray-600 leading-relaxed">
                      {trustInfo.mission}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-4">
                  <div className="bg-green-100 p-3 rounded-lg">
                    <TrendingUp className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Vision</h3>
                    <p className="text-gray-600 leading-relaxed">
                      {trustInfo.vision}
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="mt-8">
                <Button asChild>
                  <Link to="/about">
                    Learn More About Us <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </div>
            </div>
            
            <div className="relative">
              <div className="bg-gradient-to-br from-blue-50 to-green-50 p-8 rounded-2xl">
                <div className="grid grid-cols-2 gap-6">
                  <div className="text-center">
                    <div className="bg-blue-600 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                      <GraduationCap className="h-8 w-8 text-white" />
                    </div>
                    <h4 className="font-semibold text-gray-900">Education</h4>
                    <p className="text-sm text-gray-600 mt-1">Building futures through learning</p>
                  </div>
                  
                  <div className="text-center">
                    <div className="bg-green-600 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                      <Stethoscope className="h-8 w-8 text-white" />
                    </div>
                    <h4 className="font-semibold text-gray-900">Healthcare</h4>
                    <p className="text-sm text-gray-600 mt-1">Ensuring healthy communities</p>
                  </div>
                  
                  <div className="text-center">
                    <div className="bg-cyan-600 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                      <Droplets className="h-8 w-8 text-white" />
                    </div>
                    <h4 className="font-semibold text-gray-900">Clean Water</h4>
                    <p className="text-sm text-gray-600 mt-1">Access to safe drinking water</p>
                  </div>
                  
                  <div className="text-center">
                    <div className="bg-purple-600 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                      <HandHeart className="h-8 w-8 text-white" />
                    </div>
                    <h4 className="font-semibold text-gray-900">Community</h4>
                    <p className="text-sm text-gray-600 mt-1">Empowering local development</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Programs */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Our Programs
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Discover how we're making a difference through our comprehensive programs
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {programs.slice(0, 3).map((program) => (
              <Card key={program.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                <div className="aspect-video bg-gradient-to-br from-blue-100 to-green-100 flex items-center justify-center">
                  <div className="text-center">
                    <div className="bg-white p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center shadow-md">
                      {program.category === 'education' && <GraduationCap className="h-8 w-8 text-blue-600" />}
                      {program.category === 'healthcare' && <Stethoscope className="h-8 w-8 text-green-600" />}
                      {program.category === 'environment' && <Droplets className="h-8 w-8 text-cyan-600" />}
                    </div>
                    <h3 className="font-semibold text-gray-900">{program.title}</h3>
                  </div>
                </div>
                <CardContent className="p-6">
                  <p className="text-gray-600 mb-4 leading-relaxed">
                    {program.description}
                  </p>
                  <div className="flex justify-between items-center text-sm text-gray-500 mb-4">
                    <span>{program.beneficiaries.toLocaleString()} beneficiaries</span>
                    <span className="capitalize">{program.status}</span>
                  </div>
                  <Button variant="outline" className="w-full">
                    Learn More
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
          
          <div className="text-center mt-12">
            <Button asChild size="lg">
              <Link to="/programs">
                View All Programs <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl lg:text-4xl font-bold mb-6">
            Ready to Make a Difference?
          </h2>
          <p className="text-xl mb-8 text-blue-100 max-w-2xl mx-auto">
            Join thousands of supporters who are helping us create positive change in communities worldwide
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" asChild className="bg-white text-blue-600 hover:bg-gray-100">
              <Link to="/donate">
                Make a Donation <Heart className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button size="lg" variant="outline" asChild className="border-white text-white hover:bg-white hover:text-blue-600">
              <Link to="/volunteer">
                Volunteer With Us <Users className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
