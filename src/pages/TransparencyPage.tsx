import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Download, FileText, Pie<PERSON><PERSON>, TrendingUp } from 'lucide-react';
import { financialReports } from '@/data/mockData';

export function TransparencyPage() {
  return (
    <div className="space-y-0">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-green-600 to-teal-800 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl lg:text-5xl font-bold mb-6">Financial Transparency</h1>
            <p className="text-xl text-green-100 leading-relaxed">
              We believe in complete transparency. View our financial reports, impact data, and accountability measures
            </p>
          </div>
        </div>
      </section>

      {/* Financial Overview */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">2023 Financial Overview</h2>
          </div>
          
          <div className="grid md:grid-cols-4 gap-6 mb-12">
            <Card className="text-center p-6">
              <CardContent className="p-0">
                <div className="text-3xl font-bold text-green-600 mb-2">$2.5M</div>
                <div className="text-gray-600">Total Revenue</div>
              </CardContent>
            </Card>
            <Card className="text-center p-6">
              <CardContent className="p-0">
                <div className="text-3xl font-bold text-blue-600 mb-2">85%</div>
                <div className="text-gray-600">Program Expenses</div>
              </CardContent>
            </Card>
            <Card className="text-center p-6">
              <CardContent className="p-0">
                <div className="text-3xl font-bold text-purple-600 mb-2">10%</div>
                <div className="text-gray-600">Administrative</div>
              </CardContent>
            </Card>
            <Card className="text-center p-6">
              <CardContent className="p-0">
                <div className="text-3xl font-bold text-orange-600 mb-2">5%</div>
                <div className="text-gray-600">Fundraising</div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Reports */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Annual Reports</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Download our comprehensive annual reports and financial statements
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            {financialReports.map((report) => (
              <Card key={report.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <FileText className="h-6 w-6 text-blue-600" />
                    <span>{report.title}</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">Total Revenue:</span>
                      <div className="font-semibold">${(report.summary.totalRevenue / 1000000).toFixed(1)}M</div>
                    </div>
                    <div>
                      <span className="text-gray-500">Program Expenses:</span>
                      <div className="font-semibold">${(report.summary.programExpenses / 1000000).toFixed(1)}M</div>
                    </div>
                    <div>
                      <span className="text-gray-500">Admin Expenses:</span>
                      <div className="font-semibold">${(report.summary.administrativeExpenses / 1000).toFixed(0)}K</div>
                    </div>
                    <div>
                      <span className="text-gray-500">Net Assets:</span>
                      <div className="font-semibold">${(report.summary.netAssets / 1000).toFixed(0)}K</div>
                    </div>
                  </div>
                  
                  <Button className="w-full">
                    <Download className="mr-2 h-4 w-4" />
                    Download Report
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}
