import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Users, 
  Clock, 
  MapPin, 
  Heart,
  CheckCircle,
  ArrowRight
} from 'lucide-react';

const volunteerOpportunities = [
  {
    id: '1',
    title: 'Education Program Assistant',
    description: 'Help teach basic literacy and numeracy skills to children in rural schools',
    timeCommitment: '4-6 hours/week',
    location: 'Rural Schools',
    skills: ['Teaching', 'Patience', 'Communication'],
    urgent: false
  },
  {
    id: '2',
    title: 'Healthcare Support Volunteer',
    description: 'Assist medical staff in mobile clinics and health education programs',
    timeCommitment: '6-8 hours/week',
    location: 'Mobile Clinics',
    skills: ['Healthcare Background', 'Empathy', 'Organization'],
    urgent: true
  },
  {
    id: '3',
    title: 'Community Outreach Coordinator',
    description: 'Help organize community events and awareness programs',
    timeCommitment: '3-5 hours/week',
    location: 'Various Communities',
    skills: ['Event Planning', 'Communication', 'Leadership'],
    urgent: false
  }
];

export function VolunteerPage() {
  return (
    <div className="space-y-0">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-purple-600 to-blue-800 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl lg:text-5xl font-bold mb-6">Volunteer With Us</h1>
            <p className="text-xl text-purple-100 leading-relaxed">
              Join our community of dedicated volunteers and make a direct impact in the lives of those who need it most
            </p>
          </div>
        </div>
      </section>

      {/* Why Volunteer */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Why Volunteer?</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Volunteering with us offers meaningful ways to contribute to positive change while developing new skills and connections
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            <Card className="text-center p-6">
              <CardContent className="p-0">
                <div className="bg-blue-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                  <Heart className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Make a Difference</h3>
                <p className="text-gray-600">
                  Directly impact lives and communities through meaningful volunteer work
                </p>
              </CardContent>
            </Card>
            
            <Card className="text-center p-6">
              <CardContent className="p-0">
                <div className="bg-green-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                  <Users className="h-8 w-8 text-green-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Build Community</h3>
                <p className="text-gray-600">
                  Connect with like-minded individuals who share your passion for helping others
                </p>
              </CardContent>
            </Card>
            
            <Card className="text-center p-6">
              <CardContent className="p-0">
                <div className="bg-purple-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                  <CheckCircle className="h-8 w-8 text-purple-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Develop Skills</h3>
                <p className="text-gray-600">
                  Gain valuable experience and develop new skills while contributing to important causes
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Volunteer Opportunities */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Current Opportunities</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Find the perfect volunteer opportunity that matches your skills, interests, and availability
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {volunteerOpportunities.map((opportunity) => (
              <Card key={opportunity.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <CardTitle className="text-lg">{opportunity.title}</CardTitle>
                    {opportunity.urgent && (
                      <Badge variant="destructive" className="text-xs">Urgent</Badge>
                    )}
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-gray-600 leading-relaxed">
                    {opportunity.description}
                  </p>
                  
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-gray-400" />
                      <span>{opportunity.timeCommitment}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <MapPin className="h-4 w-4 text-gray-400" />
                      <span>{opportunity.location}</span>
                    </div>
                  </div>
                  
                  <div>
                    <p className="text-sm font-medium text-gray-700 mb-2">Skills needed:</p>
                    <div className="flex flex-wrap gap-1">
                      {opportunity.skills.map((skill) => (
                        <Badge key={skill} variant="secondary" className="text-xs">
                          {skill}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  
                  <Button className="w-full">
                    Apply Now <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Volunteer Process */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">How to Get Started</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Our simple process makes it easy to start volunteering and making a difference
            </p>
          </div>
          
          <div className="max-w-4xl mx-auto">
            <div className="grid md:grid-cols-4 gap-8">
              <div className="text-center">
                <div className="bg-blue-600 text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                  1
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Apply</h3>
                <p className="text-gray-600 text-sm">Fill out our volunteer application form</p>
              </div>
              
              <div className="text-center">
                <div className="bg-green-600 text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                  2
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Interview</h3>
                <p className="text-gray-600 text-sm">Brief interview to match you with the right opportunity</p>
              </div>
              
              <div className="text-center">
                <div className="bg-purple-600 text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                  3
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Training</h3>
                <p className="text-gray-600 text-sm">Receive orientation and training for your role</p>
              </div>
              
              <div className="text-center">
                <div className="bg-orange-600 text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                  4
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Start</h3>
                <p className="text-gray-600 text-sm">Begin making a difference in your community</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-gradient-to-r from-purple-600 to-blue-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">Ready to Make a Difference?</h2>
          <p className="text-xl mb-8 text-purple-100 max-w-2xl mx-auto">
            Join our volunteer community and help us create positive change in communities worldwide
          </p>
          <Button size="lg" className="bg-white text-purple-600 hover:bg-gray-100">
            Start Your Volunteer Application
          </Button>
        </div>
      </section>
    </div>
  );
}
