import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  GraduationCap, 
  Stethoscope, 
  Droplets, 
  HandHeart,
  Users,
  Calendar,
  MapPin,
  DollarSign
} from 'lucide-react';
import { programs, type ProgramCategory } from '@/data/mockData';

const categoryIcons = {
  education: GraduationCap,
  healthcare: Stethoscope,
  environment: Droplets,
  'poverty-alleviation': HandHeart,
  'disaster-relief': HandHeart,
  'community-development': Users
};

const categoryColors = {
  education: 'bg-blue-600',
  healthcare: 'bg-green-600',
  environment: 'bg-cyan-600',
  'poverty-alleviation': 'bg-purple-600',
  'disaster-relief': 'bg-red-600',
  'community-development': 'bg-orange-600'
};

export function ProgramsPage() {
  const [selectedCategory, setSelectedCategory] = useState<ProgramCategory | 'all'>('all');
  
  const categories: (ProgramCategory | 'all')[] = [
    'all',
    'education',
    'healthcare',
    'environment',
    'poverty-alleviation',
    'community-development',
    'disaster-relief'
  ];

  const filteredPrograms = selectedCategory === 'all' 
    ? programs 
    : programs.filter(program => program.category === selectedCategory);

  return (
    <div className="space-y-0">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-green-600 to-blue-800 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl lg:text-5xl font-bold mb-6">Our Programs</h1>
            <p className="text-xl text-green-100 leading-relaxed">
              Discover our comprehensive programs designed to create lasting positive change in communities worldwide
            </p>
          </div>
        </div>
      </section>

      {/* Category Filter */}
      <section className="py-8 bg-gray-50 border-b">
        <div className="container mx-auto px-4">
          <div className="flex flex-wrap gap-2 justify-center">
            {categories.map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? "default" : "outline"}
                onClick={() => setSelectedCategory(category)}
                className="capitalize"
              >
                {category === 'all' ? 'All Programs' : category.replace('-', ' ')}
              </Button>
            ))}
          </div>
        </div>
      </section>

      {/* Programs Grid */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredPrograms.map((program) => {
              const IconComponent = categoryIcons[program.category as keyof typeof categoryIcons] || HandHeart;
              const colorClass = categoryColors[program.category as keyof typeof categoryColors] || 'bg-gray-600';
              
              return (
                <Card key={program.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="aspect-video bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                    <div className="text-center">
                      <div className={`${colorClass} p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center`}>
                        <IconComponent className="h-8 w-8 text-white" />
                      </div>
                      <h3 className="font-semibold text-gray-900">{program.title}</h3>
                    </div>
                  </div>
                  
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between mb-3">
                      <Badge variant="secondary" className="capitalize">
                        {program.category.replace('-', ' ')}
                      </Badge>
                      <Badge variant={program.status === 'active' ? 'default' : 'outline'}>
                        {program.status}
                      </Badge>
                    </div>
                    
                    <p className="text-gray-600 mb-4 leading-relaxed">
                      {program.description}
                    </p>
                    
                    <div className="space-y-2 mb-4 text-sm text-gray-500">
                      <div className="flex items-center space-x-2">
                        <Users className="h-4 w-4" />
                        <span>{program.beneficiaries.toLocaleString()} beneficiaries</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <MapPin className="h-4 w-4" />
                        <span>{program.location}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-4 w-4" />
                        <span>Started {new Date(program.startDate).getFullYear()}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <DollarSign className="h-4 w-4" />
                        <span>Budget: ${program.budget.toLocaleString()}</span>
                      </div>
                    </div>
                    
                    <Button className="w-full">
                      Learn More
                    </Button>
                  </CardContent>
                </Card>
              );
            })}
          </div>
          
          {filteredPrograms.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500 text-lg">No programs found in this category.</p>
            </div>
          )}
        </div>
      </section>

      {/* Impact Summary */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Program Impact</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Our programs have created measurable positive change across multiple sectors
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="text-center p-6">
              <CardContent className="p-0">
                <div className="text-3xl font-bold text-blue-600 mb-2">
                  {programs.reduce((sum, program) => sum + program.beneficiaries, 0).toLocaleString()}
                </div>
                <div className="text-gray-600">Total Beneficiaries</div>
              </CardContent>
            </Card>
            
            <Card className="text-center p-6">
              <CardContent className="p-0">
                <div className="text-3xl font-bold text-green-600 mb-2">
                  {programs.filter(p => p.status === 'active').length}
                </div>
                <div className="text-gray-600">Active Programs</div>
              </CardContent>
            </Card>
            
            <Card className="text-center p-6">
              <CardContent className="p-0">
                <div className="text-3xl font-bold text-purple-600 mb-2">
                  ${(programs.reduce((sum, program) => sum + program.budget, 0) / 1000000).toFixed(1)}M
                </div>
                <div className="text-gray-600">Total Investment</div>
              </CardContent>
            </Card>
            
            <Card className="text-center p-6">
              <CardContent className="p-0">
                <div className="text-3xl font-bold text-orange-600 mb-2">
                  {new Set(programs.map(p => p.location)).size}
                </div>
                <div className="text-gray-600">Locations Served</div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </div>
  );
}
